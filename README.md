# 文档学习小模型 (Document Learning Small Model)

一个能够通过阅读文档学习技能的小型语言模型。给它一个JavaScript文档，它就能学会使用JavaScript！

## 🌟 特性

- **文档学习**: 能够从技术文档中学习概念和语法
- **多任务训练**: 支持掩码语言建模和问答任务
- **小型架构**: 参数量控制在100M以下，适合个人设备训练
- **异步处理**: 高效的文档处理和数据准备
- **交互式推理**: 支持实时对话和文档学习

## 🏗️ 架构

- **模型**: 基于Transformer的小型语言模型
- **训练**: 预训练 + 微调的两阶段训练
- **数据**: 支持Markdown、纯文本和网页文档
- **推理**: 支持文本生成、问答、代码生成等多种任务

## 📦 安装

1. 克隆项目:
```bash
git clone <repository-url>
cd ai-model
```

2. 安装依赖:
```bash
pip install -r requirements.txt
```

3. 配置环境:
```bash
# 可选：配置wandb用于训练监控
wandb login
```

## 🚀 快速开始

### 1. 训练模型

使用JavaScript文档训练模型：

```bash
python train.py --documents \
    "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction" \
    "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Grammar_and_types" \
    "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Control_flow_and_error_handling" \
    --num_epochs 5 \
    --batch_size 8
```

使用本地文档：

```bash
python train.py --documents \
    "./docs/javascript_basics.md" \
    "./docs/functions.md" \
    "./docs/objects.md"
```

### 2. 测试模型

基本功能测试：

```bash
python test_model.py ./models/best_model
```

交互式测试：

```bash
python test_model.py ./models/best_model --interactive
```

测试文档学习：

```bash
python test_model.py ./models/best_model \
    --test_document "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Functions"
```

### 3. 使用模型

```python
from src.inference import DocumentLearnerInference

# 加载模型
inferencer = DocumentLearnerInference("./models/best_model")

# 学习新文档
result = await inferencer.learn_from_document("path/to/document.md")

# 回答问题
answer = inferencer.answer_question("什么是JavaScript函数？")

# 生成代码
code = inferencer.generate_code("创建一个计算斐波那契数列的函数", "javascript")

# 解释概念
explanation = inferencer.explain_concept("闭包")
```

## ⚙️ 配置

主要配置在 `config.yaml` 中：

```yaml
# 模型配置
model:
  hidden_size: 512          # 隐藏层大小
  num_hidden_layers: 8      # 层数
  num_attention_heads: 8    # 注意力头数

# 训练配置
training:
  batch_size: 16           # 批次大小
  learning_rate: 5e-4      # 学习率
  num_epochs: 10           # 训练轮数

# 数据配置
data:
  max_seq_length: 1024     # 最大序列长度
  doc_chunk_size: 512      # 文档块大小
```

## 📊 训练监控

项目集成了Wandb用于训练监控：

- 训练损失和验证损失
- 学习率变化
- 模型性能指标

## 🔧 高级用法

### 自定义数据处理

```python
from src.data_processor import DocumentProcessor, DatasetBuilder

processor = DocumentProcessor()
builder = DatasetBuilder(processor)

# 处理自定义文档
chunks = await processor.process_markdown_file("custom_doc.md")
samples = await builder.build_training_dataset(["doc1.md", "doc2.md"])
```

### 自定义模型架构

```python
from src.model import DocumentLearnerConfig, DocumentLearnerModel

# 自定义配置
config = DocumentLearnerConfig(
    hidden_size=256,
    num_hidden_layers=6,
    num_attention_heads=4
)

# 创建模型
model = DocumentLearnerModel(config)
```

### 继续训练

```bash
python train.py --documents "new_docs.md" \
    --resume_from "./outputs/checkpoint-1000"
```

## 📁 项目结构

```
ai-model/
├── src/
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── data_processor.py  # 数据处理
│   ├── model.py          # 模型架构
│   ├── trainer.py        # 训练逻辑
│   └── inference.py      # 推理接口
├── train.py              # 训练脚本
├── test_model.py         # 测试脚本
├── config.yaml           # 配置文件
├── requirements.txt      # 依赖列表
└── README.md            # 说明文档
```

## 🎯 使用场景

1. **技术文档学习**: 快速学习新的编程语言或框架
2. **代码助手**: 基于文档生成代码示例
3. **概念解释**: 解释技术概念和术语
4. **问答系统**: 基于文档内容回答技术问题

## 🔍 示例对话

```
你: learn https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Functions
助手: 已学习文档 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Functions'
文档摘要: 该文档介绍了JavaScript中函数的定义、调用和使用方法...
学到的概念: 函数声明, 函数表达式, 箭头函数, 参数, 返回值

你: 如何创建一个JavaScript函数？
助手: 在JavaScript中，有几种创建函数的方法：

1. 函数声明：
function myFunction() {
    // 函数体
}

2. 函数表达式：
const myFunction = function() {
    // 函数体
};

3. 箭头函数：
const myFunction = () => {
    // 函数体
};

你: 生成一个计算圆面积的函数
助手: function calculateCircleArea(radius) {
    return Math.PI * radius * radius;
}
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

- Hugging Face Transformers
- PyTorch
- 所有开源贡献者
