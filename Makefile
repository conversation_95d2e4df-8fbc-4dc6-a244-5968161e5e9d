# 文档学习模型 Makefile

.PHONY: help install train test clean example

# 默认目标
help:
	@echo "文档学习模型 - 可用命令:"
	@echo ""
	@echo "  make install     - 安装依赖和设置环境"
	@echo "  make train       - 训练模型 (使用默认JavaScript文档)"
	@echo "  make train-local - 使用本地文档训练"
	@echo "  make test        - 测试训练好的模型"
	@echo "  make example     - 运行完整示例"
	@echo "  make interactive - 启动交互式模式"
	@echo "  make clean       - 清理生成的文件"
	@echo "  make help        - 显示此帮助信息"

# 安装依赖
install:
	@echo "🚀 开始安装..."
	python setup.py

# 训练模型 (默认JavaScript文档)
train:
	@echo "🚀 开始训练模型..."
	python train.py --documents \
		"https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction" \
		"https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Grammar_and_types" \
		"https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Control_flow_and_error_handling" \
		--num_epochs 5 \
		--batch_size 8

# 使用本地文档训练
train-local:
	@echo "🚀 使用本地文档训练..."
	@if [ ! -f "data/javascript_basics.md" ]; then \
		echo "创建示例文档..."; \
		python -c "from example import create_sample_documents; create_sample_documents()"; \
	fi
	python train.py --documents data/javascript_basics.md data/python_basics.md

# 测试模型
test:
	@echo "🧪 测试模型..."
	@if [ -d "models/best_model" ]; then \
		python test_model.py models/best_model; \
	else \
		echo "❌ 找不到训练好的模型，请先运行 make train"; \
	fi

# 交互式模式
interactive:
	@echo "🤖 启动交互式模式..."
	@if [ -d "models/best_model" ]; then \
		python test_model.py models/best_model --interactive; \
	else \
		echo "❌ 找不到训练好的模型，请先运行 make train"; \
	fi

# 运行示例
example:
	@echo "🎯 运行示例..."
	python example.py

# 清理文件
clean:
	@echo "🧹 清理生成的文件..."
	rm -rf outputs/
	rm -rf models/
	rm -rf logs/
	rm -rf cache/
	rm -rf __pycache__/
	rm -rf src/__pycache__/
	find . -name "*.pyc" -delete
	find . -name "*.pyo" -delete
	@echo "✅ 清理完成"

# 快速开始 (安装 + 训练 + 测试)
quickstart: install train test
	@echo "🎉 快速开始完成！"

# 开发模式安装
dev-install:
	@echo "🔧 开发模式安装..."
	pip install -r requirements.txt
	pip install jupyter notebook ipython
	@echo "✅ 开发环境安装完成"

# 检查代码质量
lint:
	@echo "🔍 检查代码质量..."
	@if command -v flake8 >/dev/null 2>&1; then \
		flake8 src/ --max-line-length=100; \
	else \
		echo "⚠ flake8 未安装，跳过代码检查"; \
	fi

# 运行所有测试
test-all: test
	@echo "🧪 运行所有测试..."
	python -m pytest tests/ -v || echo "⚠ 没有找到测试文件"

# 创建示例数据
create-data:
	@echo "📄 创建示例数据..."
	python -c "from example import create_sample_documents; create_sample_documents()"

# 显示项目状态
status:
	@echo "📊 项目状态:"
	@echo "Python版本: $(shell python --version)"
	@echo "PyTorch版本: $(shell python -c 'import torch; print(torch.__version__)' 2>/dev/null || echo '未安装')"
	@echo "GPU可用: $(shell python -c 'import torch; print(torch.cuda.is_available())' 2>/dev/null || echo '未知')"
	@echo ""
	@echo "文件状态:"
	@echo "  配置文件: $(shell [ -f config.yaml ] && echo '✅' || echo '❌')"
	@echo "  训练数据: $(shell [ -d data ] && echo '✅' || echo '❌')"
	@echo "  训练模型: $(shell [ -d models/best_model ] && echo '✅' || echo '❌')"

# 备份模型
backup:
	@echo "💾 备份模型..."
	@if [ -d "models/best_model" ]; then \
		tar -czf "model_backup_$(shell date +%Y%m%d_%H%M%S).tar.gz" models/; \
		echo "✅ 模型已备份"; \
	else \
		echo "❌ 没有找到要备份的模型"; \
	fi
